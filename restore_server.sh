#!/bin/bash
# ==============================================================================
# СКРИПТ ПОЛНОГО ВОССТАНОВЛЕНИЯ СЕРВЕРА QUER.US
# Версия без искажений
# ==============================================================================
set -e

echo "--- ФАЗА 1: НАСТРОЙКА NGINX ---"

# Шаг 1.1: Удаляем старые, потенциально конфликтные конфигурации
echo "⚙️ Очистка старых конфигураций Nginx..."
sudo rm -f /etc/nginx/sites-available/quer.us
sudo rm -f /etc/nginx/sites-enabled/quer.us
sudo rm -f /etc/nginx/conf.d/quer.us.conf

# Шаг 1.2: Копируем новую, полную конфигурацию
echo "⚙️ Создание нового конфигурационного файла..."
sudo cp /tmp/nginx_config.conf /etc/nginx/sites-available/quer.us

# Шаг 1.3: Активируем новую конфигурацию
echo "⚙️ Активация новой конфигурации..."
sudo ln -s /etc/nginx/sites-available/quer.us /etc/nginx/sites-enabled/

# Шаг 1.4: Проверяем синтаксис и перезапускаем Nginx
echo "🚀 Проверка и перезапуск Nginx..."
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl status nginx --no-pager
echo "✅ Nginx успешно настроен."
echo ""
echo "--- ФАЗА 2: ДЕПЛОЙ И ЗАПУСК ПРИЛОЖЕНИЯ ASTRO ---"

# Шаг 2.1: Останавливаем сервис, если он запущен
sudo systemctl stop astro-blue.service

# Шаг 2.2: Архивируем старую версию на случай отката
sudo mv /var/www/quer.us/blue/app /var/www/quer.us/blue/app-backup-$(date +%F-%H%M) 2>/dev/null || true

# Шаг 2.3: Распаковываем новый артефакт в чистую директорию
echo "📦 Распаковка нового артефакта..."
sudo mkdir -p /var/www/quer.us/blue/app
sudo tar -xzf /tmp/deployment_artifact.tar.gz -C /var/www/quer.us/blue/app

# Шаг 2.4: Устанавливаем зависимости НА СЕРВЕРЕ для правильной архитектуры
echo "⏳ Установка зависимостей, совместимых с Ubuntu/AMD64..."
cd /var/www/quer.us/blue/app && sudo npm install --omit=dev

# Шаг 2.5: Восстанавливаем .env из самого свежего бэкапа
BACKUP_ENV=$(sudo find /var/www/quer.us/blue/ -maxdepth 2 -type f -name ".env" -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
if [ -n "$BACKUP_ENV" ]; then
    echo "Найден .env бэкап: $BACKUP_ENV. Копируем..."
    sudo cp "$BACKUP_ENV" /var/www/quer.us/blue/app/.env
fi

# Шаг 2.6: Устанавливаем правильные права
sudo chown -R www-data:www-data /var/www/quer.us/blue/app

# Шаг 2.7: Обновляем и запускаем сервис systemd
echo "⚙️ Настройка и запуск сервиса Astro..."
sudo sed -i 's|ExecStart=.*|ExecStart=/usr/bin/node dist/server/entry.mjs|' /etc/systemd/system/astro-blue.service
sudo sed -i 's|WorkingDirectory=.*|WorkingDirectory=/var/www/quer.us/blue/app|' /etc/systemd/system/astro-blue.service
sudo systemctl daemon-reload
sudo systemctl start astro-blue.service

echo "✅ Приложение Astro развернуто."
echo ""
echo "--- ФАЗА 3: ФИНАЛЬНАЯ ВЕРИФИКАЦИЯ ---"
sleep 5 # Даем сервису время на запуск

echo "📊 Проверка финального статуса сервиса Astro..."
sudo systemctl status astro-blue.service --no-pager

echo "---"
echo "📝 Проверка последних 20 строк лога Astro:"
sudo journalctl -u astro-blue.service -n 20 --no-pager

echo "---"
echo "🔬 Проверка локального ответа от Astro (должен быть 200 OK):"
curl -I http://localhost:3001/ || echo "Не удалось подключиться к Astro на порту 3001, это ожидаемо, если сервис упал. Смотрите лог выше."

echo "✅ Восстановление завершено. Проверьте сайт в браузере."