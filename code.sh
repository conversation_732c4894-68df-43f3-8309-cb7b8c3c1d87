# ==============================================================================
# ФИНАЛЬНЫЙ СКРИПТ ВОССТАНОВЛЕНИЯ v17.0 (Убийство зомби + создание .env)
# ==============================================================================

# --- Шаг 1: Локальная генерация .env файлов ---
echo "⚙️  Генерация правильных .env файлов для бэкендов..."
cd /Users/<USER>/quer-calc-ui/
./config/generate-all.sh generate production blue

# --- Шаг 2: Загрузка .env файлов на сервер ---
echo "🚀 Загрузка .env файлов на сервер..."
scp /Users/<USER>/quer-calc-ui/go-landing-backend/.env quer-x0:/tmp/go.env
scp /Users/<USER>/quer-calc-ui/rust-backend/.env quer-x0:/tmp/rust.env

# --- Шаг 3: Убийство зомби-процессов и финальный запуск на сервере ---
echo -e "\n✅ Выполнение финальной настройки на сервере..."
ssh quer-x0 "
    set -e
    
    # --- УБИЙСТВО ЗОМБИ ---
    echo '🔬 Поиск и остановка старых процессов...'
    sudo kill -9 \$(sudo lsof -t -i:8091) 2>/dev/null || echo 'Порт 8091 свободен.'
    sudo kill -9 \$(sudo lsof -t -i:8082) 2>/dev/null || echo 'Порт 8082 свободен.'
    sleep 2

    # --- РАЗМЕЩЕНИЕ .ENV ФАЙЛОВ ---
    echo '⚙️  Размещение .env файлов...'
    sudo cp /tmp/go.env /var/www/quer.us/blue/backend/.env
    sudo cp /tmp/rust.env /var/www/quer.us/blue/terminal-backend/.env
    sudo chown www-data:www-data /var/www/quer.us/blue/backend/.env
    sudo chown www-data:www-data /var/www/quer.us/blue/terminal-backend/.env

    # --- ПЕРЕЗАПУСК ВСЕХ СЕРВИСОВ ---
    echo '🚀 Перезапуск всех сервисов в правильном порядке...'
    sudo systemctl restart quer-backend-blue.service
    sudo systemctl restart quer-terminal-backend-blue.service
    sudo systemctl restart astro-blue.service
    
    # --- ФИНАЛЬНАЯ ВЕРИФИКАЦИЯ ---
    sleep 5
    echo -e '\n📊 Проверка финального статуса всех сервисов:'
    sudo systemctl status astro-blue.service quer-backend-blue.service quer-terminal-backend-blue.service --no-pager
"

echo "✅ Процесс завершен. Проверьте статус сервисов выше. Все три должны быть 'active (running)'."