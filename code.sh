ssh quer-x0 "
# Создаем временный файл с САМОЙ ФИНАЛЬНОЙ версией конфигурации Nginx
cat << 'EOF' | sudo tee /tmp/nginx_final_v25.conf
# ==============================================================
# ФИНАЛЬНАЯ КОНФИГУРАЦИЯ NGINX v25.0
# Добавлена обработка config.json для React-приложения
# ==============================================================
upstream astro_backend_blue { server 127.0.0.1:3001; }
upstream go_backend_blue { server 127.0.0.1:8091; }
upstream rust_backend_blue { server 127.0.0.1:8082; }

server {
    listen 80;
    server_name quer.us www.quer.us;
    return 301 https://\\\$host\\\$request_uri;
}
server {
    listen 443 ssl http2;
    server_name quer.us www.quer.us;

    ssl_certificate /etc/ssl/cloudflare/quer.us.pem;
    ssl_certificate_key /etc/ssl/cloudflare/quer.us.key;

    add_header Content-Security-Policy \"script-src 'self' 'unsafe-inline' https://clerk.quer.us; worker-src 'self' blob:; frame-src 'self' https://clerk.quer.us; object-src 'none'; base-uri 'self';\" always;
    add_header X-Frame-Options \"SAMEORIGIN\" always;
    add_header X-Content-Type-Options \"nosniff\" always;

    location /__clerk {
        rewrite ^/__clerk/(.*)\$ /\$1 break;
        proxy_pass https://frontend-api.clerk.dev;
        proxy_ssl_server_name on;
        proxy_set_header Host frontend-api.clerk.dev;
        proxy_set_header X-Real-IP \\\$remote_addr;
        proxy_set_header X-Forwarded-For \\\$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \\\$scheme;
    }

    # === React Calculator (терминал) - ИСПРАВЛЕНО ===
    # Сначала обрабатываем конкретный файл config.json
    location = /terminal/config.json {
        root /var/www/quer.us/blue/public;
    }
    # Затем все остальные запросы в /terminal
    location /terminal/ {
        root /var/www/quer.us/blue/public;
        try_files \\\$uri \\\$uri/ /terminal/index.html;
    }

    # === ИСПРАВЛЕННАЯ МАРШРУТИЗАЦИЯ API ===
    location /api/terminal/calculate {
        proxy_pass http://rust_backend_blue/api/calculate;
        proxy_set_header Host \\\$server_name;
        proxy_set_header X-Real-IP \\\$remote_addr;
    }
    location /api/ {
        proxy_pass http://go_backend_blue;
        proxy_set_header Host \\\$server_name;
        proxy_set_header X-Real-IP \\\$remote_addr;
    }
    
    location / {
        proxy_pass http://astro_backend_blue;
        proxy_set_header Host \\\$host;
        proxy_set_header X-Real-IP \\\$remote_addr;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \\\$http_upgrade;
        proxy_set_header Connection 'upgrade';
    }
}
EOF

# Атомарно заменяем старый конфиг новым
sudo mv /tmp/nginx_final_v25.conf /etc/nginx/sites-available/quer.us

# Проверяем и перезапускаем Nginx
echo '🚀 Проверка и перезапуск Nginx...'
sudo nginx -t && sudo systemctl restart nginx && echo '✅ Nginx успешно перезапущен!'
"

echo "✅ Финальное исправление Nginx применено. Пожалуйста, полностью закройте и заново откройте Desktop-приложение."