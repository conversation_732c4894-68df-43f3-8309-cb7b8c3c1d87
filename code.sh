ssh quer-x0 "
    set -e
    echo '⚙️  Остановка и очистка...'
    sudo systemctl stop astro-blue.service
    sudo rm -rf /var/www/quer.us/blue/app
    sudo mkdir -p /var/www/quer.us/blue/app
    
    echo '📦 Распаковка нового артефакта...'
    sudo tar -xzf /tmp/deployment_artifact.tar.gz -C /var/www/quer.us/blue/app

    echo '⏳ Установка нативных зависимостей на сервере...'
    cd /var/www/quer.us/blue/app && sudo npm install --omit=dev
    
    echo '⚙️  Восстановление .env и настройка прав...'
    BACKUP_ENV=\$(sudo find /var/www/quer.us/blue/ -maxdepth 2 -type f -name '.env' -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [ -n \"\$BACKUP_ENV\" ]; then
        sudo cp \"\$BACKUP_ENV\" /var/www/quer.us/blue/app/.env
    fi
    sudo chown -R www-data:www-data /var/www/quer.us/blue/app
    
    echo '🚀 Настройка и запуск сервиса...'
    sudo sed -i 's|WorkingDirectory=.*|WorkingDirectory=/var/www/quer.us/blue/app|' /etc/systemd/system/astro-blue.service
    sudo sed -i 's|ExecStart=.*|ExecStart=/usr/bin/node server/entry.mjs|' /etc/systemd/system/astro-blue.service
    
    sudo systemctl daemon-reload
    sudo systemctl start astro-blue.service

    sleep 5
    echo -e \"\n--- ФИНАЛЬНАЯ ПРОВЕРКА ---\"
    echo '📊 Статус сервиса Astro:'
    sudo systemctl status astro-blue.service --no-pager
"